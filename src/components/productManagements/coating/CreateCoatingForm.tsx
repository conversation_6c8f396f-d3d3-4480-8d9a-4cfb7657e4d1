'use client';

import React, { useEffect, useState } from 'react';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';

import { Button } from '@/components/ui/button';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { ApiResponse, ValidateFileType } from '@/utils/types';
import { validateFile } from '@/utils/validateFiles';
import { isNull, isUndefined } from 'lodash';
import fileUploadService from '@/services/fileUploader';
import { Toast } from '@/utils/toast';
import ImageUploadComponent from '@/components/shared/image-upload';
import { useRouter } from 'next/navigation';
import { Label } from '@/components/ui/label';
import Swal from 'sweetalert2';
import coatingService, { CoatingRequest } from '@/services/coating';
import { Textarea } from '@/components/ui/textarea';
import {
  inputNumberFormat,
  inputNumberFormatWithCommas,
  getFormattedDisplayValue,
  createNumberInputHandler,
  createFieldNumberInputHandler,
  onKeyDown
} from '@/utils/input-number';

type PropType = {
  action: string;
  id?: number;
};

const FormSchema = z.object({
  name: z
    .string({
      required_error: 'กรุณากรอกชื่อ',
    })
    .min(1, 'กรุณากรอกชื่อ'),
  image: z
    .string({
      required_error: 'กรุณาอัปโหลดรูปภาพ',
    })
    .min(1, 'กรุณาอัปโหลดรูปภาพ'),
  description: z
    .string({
      required_error: 'กรุณากรอกรายละเอียดการเคลือบ',
    })
    .min(1, 'กรุณากรอกรายละเอียดการเคลือบ'),

  price: z.coerce
    .number({
      required_error: 'กรุณากรอกราคา',
      invalid_type_error: 'กรุณากรอกราคา เป็นตัวเลข',
    })
    .gte(0, 'กรุณากรอกราคาเริ่มต้น 0.00 บาท'),
});

const CreateCoatingForm = ({ action, id }: PropType) => {
  const { push } = useRouter();
  const [fileUpload, setFileUpload] = useState<any>(null);
  const [coating, setCoating] = useState<any>(null);

  const form = useForm<z.infer<typeof FormSchema>>({
    resolver: zodResolver(FormSchema),
  });

  const getCoatingById = async (id: number) => {
    const res: ApiResponse = await coatingService.getCoatingById(id);
    if (res.status) {
      form.setValue('name', res.data.name);
      form.setValue('image', res.data.imageUrl);
      form.setValue('description', res.data.description);
      form.setValue('price', res.data.price);

      setFileUpload(res.data.imageUrl);
      setCoating(res.data);
    } else {
      Toast.fire({
        title: 'เกิดข้อผิดพลาด',
        text: res.message,
        icon: 'error',
      });
    }
  };

  useEffect(() => {
    form.reset();
    setFileUpload(null);

    if (action === 'edit' && !isUndefined(id)) {
      getCoatingById(id);
    }
  }, [action, id]);

  const onSubmit = async (values: z.infer<typeof FormSchema>) => {
    const req: CoatingRequest = {
      name: values.name,
      imageUrl: values.image,
      description: values.description,
      price: values.price,
    };

    if (action === 'edit') {
      req.isActive = coating.isActive;
    }

    const res: ApiResponse =
      action !== 'edit'
        ? await coatingService.createCoating(req)
        : await coatingService.updateCoating(coating.id, req);

    if (!res.status) {
      Swal.fire({
        title: 'เกิดข้อผิดพลาด',
        text: res.message,
        icon: 'error',
        showConfirmButton: true,
        confirmButtonColor: '#000000',
        confirmButtonText: 'OK',
      }).then();
    } else {
      Swal.fire({
        title: 'สำเร็จ',
        text: res.message,
        icon: 'success',
        showCloseButton: true,
        showCancelButton: false,
        showConfirmButton: true,
        confirmButtonColor: '#000000',
        confirmButtonText: 'OK',
        reverseButtons: true,
      }).then(() => {
        push('/coating');
      });
    }
  };

  const handleImageChange = async (e: any) => {
    const file = e.target.files[0];

    const validate: ValidateFileType = validateFile(e.target.files);
    if (validate.status) {
      if (!isUndefined(file) && !isNull(file)) {
        const formData: FormData = new FormData();
        formData.append('file', file);

        const res: ApiResponse = await fileUploadService.upload(formData);

        if (res.status) {
          const url = res.data;
          setFileUpload(url);
          form.setValue('image', url);
        } else {
          Toast.fire({
            title: 'เกิดข้อผิดพลาด',
            text: `ไฟล์อัปโหลดผิดพลาด :${res.message}`,
            icon: 'error',
          });
        }
      }
    } else {
      Toast.fire({
        title: 'เกิดข้อผิดพลาด',
        text: validate.message,
        icon: 'error',
      });
    }
  };

  return (
    <div className="w-full h-full flex items-center justify-center">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)}>
          <div className="flex flex-col gap-[40px] w-[800px]">
            <h3 className="text-[40px] text-[#000] font-bold">
              {action !== 'edit' ? 'สร้างเคลือบ' : 'แก้ไขเคลือบ'}
            </h3>
            <div className="flex flex-col gap-[39px]">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <Label>ชื่อ</Label>
                    <FormControl>
                      <Input
                        placeholder="ชื่อเคลือบ"
                        className="resize-none"
                        {...field}
                        defaultValue={field.value || ''}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <div>
                <Label>รูปภาพ</Label>
                <ImageUploadComponent file={fileUpload}>
                  <FormField
                    control={form.control}
                    name="image"
                    render={() => (
                      <FormItem>
                        <input
                          type="file"
                          onChange={handleImageChange}
                          className="w-[140px] resize-none"
                        />
                        <FormMessage className="absolute top-[117px]" />
                      </FormItem>
                    )}
                  />
                </ImageUploadComponent>
              </div>
              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <Label>รายละเอียด</Label>
                    <FormControl>
                      <Textarea
                        placeholder="อธิบายเกี่ยวกับการเคลือบ"
                        className="resize-none"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="price"
                render={({ field }) => (
                  <FormItem>
                    <Label>ราคา (บาท)</Label>
                    <FormControl>
                      <Input
                        type="text"
                        className="resize-none"
                        placeholder="0.00"
                        {...createFieldNumberInputHandler(field, 2)}
                        onKeyDown={onKeyDown}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            <div className="flex flex-row items-center justify-between gap-[16px] w-full h-full">
              <Button
                type="button"
                variant="outline"
                className="w-full"
                onClick={() => push('/coating')}
              >
                ยกเลิก
              </Button>
              <Button type="submit" className="w-full">
                บันทึก
              </Button>
            </div>
          </div>
        </form>
      </Form>
    </div>
  );
};

export default CreateCoatingForm;
