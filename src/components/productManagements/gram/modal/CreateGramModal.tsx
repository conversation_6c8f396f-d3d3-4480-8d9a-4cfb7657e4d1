'use client';

import React, { Fragment, useEffect, useState } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { useForm } from 'react-hook-form';
import { Input } from '@/components/ui/input';
import { ActionType } from '../../../../enum/action';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from '@/components/ui/form';

import {
  inputNumberFormat,
  inputNumberFormatWithCommas,
  getFormattedDisplayValue,
  handleNumberPaste,
  createNumberInputHandler,
  createFieldNumberInputHandler,
  onKeyDown
} from '@/utils/input-number';
import { CircleMinus } from 'lucide-react';
import { Separator } from '@/components/ui/separator';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { Label } from '@/components/ui/label';
import gramService, { GramRequest } from '@/services/gram';
import { ApiResponse } from '@/utils/types';
import Swal from 'sweetalert2';
import { isUndefined } from 'lodash';
import { Toast } from '@/utils/toast';
import { Skeleton } from '@/components/ui/skeleton';

type Props = {
  setOpenModal: (e: boolean) => void;
  openModal: boolean;
  fetchData: () => void;
  action: string;
  id?: number;
};

const GramListSchema = z.object({
  gsm: z.coerce
    .number({
      required_error: 'กรุณากรอกแกรม',
      invalid_type_error: 'กรุณากรอกแกรม',
    })
    .gte(1, 'กรุณากรอกแกรมเริ่มต้น 1 แกรม'),
  mm: z.coerce
    .number({
      required_error: 'กรุณากรอก mm.',
      invalid_type_error: 'กรุณากรอก mm.',
    })
    .gte(0.01, 'กรุณากรอก mm เริ่มต้น 0.01 mm.'),
});

const FormSchema = z.object({
  gramConfigs: z
    .array(GramListSchema)
    .min(1, 'ต้องมีค่าคอนฟิกความหนากระดาษอย่างน้อย 1 รายการ'),
});

const CreateGramModal = ({
  setOpenModal,
  openModal,
  fetchData,
  action,
  id,
}: Props) => {
  const [gram, setGram] = useState<any>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);

  const form = useForm<z.infer<typeof FormSchema>>({
    resolver: zodResolver(FormSchema),
    mode: 'onChange',
  });

  const getGramById = async (id: number) => {
    setIsLoading(true);
    const res: ApiResponse = await gramService.getGramById(id);
    if (res.status) {
      const { gsm, mm } = res.data;

      form.setValue('gramConfigs', [{ gsm: gsm, mm: mm }]);
      setGram(res.data);
      setIsLoading(false);
    } else {
      Toast.fire({
        title: 'เกิดข้อผิดพลาด',
        text: res.message,
        icon: 'error',
      });
    }
  };

  useEffect(() => {
    if (openModal) {
      form.reset();
      form.setValue('gramConfigs', [{ gsm: 0, mm: 0 }]);

      if (action === ActionType.EDIT && !isUndefined(id)) {
        getGramById(id);
      }
    }
  }, [openModal, action]);

  const onSubmit = async (data: any) => {
    const req: GramRequest[] = [...data.gramConfigs];

    const res: ApiResponse =
      action !== ActionType.EDIT
        ? await gramService.createGram(req)
        : await gramService.updateGram(gram.id, req[0]);

    if (!res.status) {
      setOpenModal(false);
      Swal.fire({
        title: 'เกิดข้อผิดพลาด',
        text: res.message,
        icon: 'error',
        showConfirmButton: true,
        confirmButtonColor: '#000000',
        confirmButtonText: 'OK',
      }).then();
    } else {
      fetchData();
      setOpenModal(false);
      Swal.fire({
        title: 'สำเร็จ',
        text: res.message,
        icon: 'success',
        showCloseButton: true,
        showCancelButton: false,
        showConfirmButton: true,
        confirmButtonColor: '#000000',
        confirmButtonText: 'OK',
        reverseButtons: true,
      }).then();
    }
  };

  const handleRemove = (index: number) => {
    const configs = form.watch('gramConfigs');
    configs.splice(index, 1);

    if (configs.length > 0) {
      form.setValue('gramConfigs', configs);
    } else {
      form.reset();
    }
  };

  return (
    <Dialog
      onOpenChange={() => setOpenModal(false)}
      open={openModal}
      defaultOpen={openModal}
      modal
    >
      <DialogContent className="w-full">
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <DialogHeader>
              <DialogTitle>
                {action === ActionType.EDIT
                  ? 'แก้ไขความหนากระดาษ'
                  : 'สร้างความหนากระดาษ'}
              </DialogTitle>
            </DialogHeader>
            <div className="pt-5">
              <div className="flex flex-row items-center justify-between">
                <Label>ความหนากระดาษ</Label>
                {action !== ActionType.EDIT && (
                  <Button
                    type="button"
                    variant="ghost"
                    className="text-[#0050FF]"
                    onClick={() =>
                      form.setValue('gramConfigs', [
                        ...form.watch('gramConfigs'),
                        {
                          gsm: 0,
                          mm: 0,
                        },
                      ])
                    }
                  >
                    +เพิ่ม
                  </Button>
                )}
              </div>
              <div className="my-5 max-h-[450px] overflow-y-auto">
                {!isLoading ? (
                  <>
                    {form.watch('gramConfigs')?.map((config, index) => (
                      <Fragment key={index}>
                        <div className="flex flex-row gap-[16px] g items-start flex-nowrap w-full py-[16px]">
                          <div className="flex-1/2">
                            <FormField
                              control={form.control}
                              name={`gramConfigs.${index}.gsm`}
                              render={({ field }) => (
                                <FormItem>
                                  <FormControl>
                                    <Input
                                      type="text"
                                      className="resize-none"
                                      placeholder="แกรม"
                                      {...createFieldNumberInputHandler(field, 0)}
                                      onKeyDown={onKeyDown}
                                    />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                          </div>
                          <div className="flex-1/2">
                            <FormField
                              control={form.control}
                              name={`gramConfigs.${index}.mm`}
                              render={({ field }) => (
                                <FormItem>
                                  <FormControl>
                                    <Input
                                      type="text"
                                      className="resize-none"
                                      placeholder="แกรม mm."
                                      {...createFieldNumberInputHandler(field, 2)}
                                      onKeyDown={onKeyDown}
                                    />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                          </div>
                          <div className="flex justify-end">
                            <Button
                              type="button"
                              variant="outline"
                              onClick={() => handleRemove(index)}
                            >
                              <CircleMinus className="text-[#d32e2e]" />
                            </Button>
                          </div>
                        </div>
                        {form.watch('gramConfigs').length !== 1 &&
                          index + 1 !== form.watch('gramConfigs').length && (
                            <div className="w-full">
                              <Separator />
                            </div>
                          )}
                      </Fragment>
                    ))}
                  </>
                ) : (
                  <Skeleton className="h-11 w-full" />
                )}
              </div>
              <div className="py-2">
                <FormField
                  control={form.control}
                  name="gramConfigs"
                  render={({ field }) => (
                    <FormItem {...field}>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>
            <DialogFooter>
              <Button
                type="button"
                variant="secondary"
                onClick={() => setOpenModal(false)}
              >
                ยกเลิก
              </Button>
              <Button type="submit">บันทึก</Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};

export default CreateGramModal;
