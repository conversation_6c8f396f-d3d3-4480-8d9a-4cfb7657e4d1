'use client';

import React, { useEffect, useState } from 'react';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';

import { Button } from '@/components/ui/button';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { ApiResponse } from '@/utils/types';
import { isUndefined } from 'lodash';
import { Toast } from '@/utils/toast';
import { useRouter } from 'next/navigation';
import { Label } from '@/components/ui/label';
import Swal from 'sweetalert2';
import unfoldedSizeService, {
  UnfoldedSizeRequest,
} from '@/services/unfoldedSize';
import {
  inputNumberFormat,
  inputNumberFormatWithCommas,
  getFormattedDisplayValue,
  createNumberInputHandler,
  onKeyDown
} from '@/utils/input-number';

type PropType = {
  action: string;
  id?: number;
};

const FormSchema = z.object({
  name: z
    .string({
      required_error: 'กรุณากรอกชื่อ',
    })
    .min(1, 'กรุณากรอกชื่อ'),
  width: z.coerce
    .number({
      required_error: 'กรุณากรอกความกว้าง',
      invalid_type_error: 'กรุณากรอกความกว้าง',
    })
    .gte(1, 'กรุณากรอกความกว้างเริ่มต้น 1 mm.'),
  height: z.coerce
    .number({
      required_error: 'กรุณากรอกความสูง',
      invalid_type_error: 'กรุณากรอกความสูง',
    })
    .gte(1, 'กรุณากรอกความสูงเริ่มต้น 1 mm..'),
  overSizeLimit: z.coerce
    .number({
      required_error: 'กรุณากรอกความคลาดเคลื่อน',
      invalid_type_error: 'กรุณากรอกความคลาดเคลื่อน',
    })
    .gte(1, 'กรุณากรอกความคลาดเคลื่อนเริ่มต้น 1 ตารางนิ้ว'),
});

const CreateUnfoldedSizeForm = ({ action, id }: PropType) => {
  const { push } = useRouter();
  const [unfoldedSize, setCoating] = useState<any>(null);

  const form = useForm<z.infer<typeof FormSchema>>({
    resolver: zodResolver(FormSchema),
  });

  const getUnfoldedSizeById = async (id: number) => {
    const res: ApiResponse = await unfoldedSizeService.getUnfoldedSizeById(id);
    if (res.status) {
      form.setValue('name', res.data.name);
      form.setValue('width', res.data.width);
      form.setValue('height', res.data.height);
      form.setValue('overSizeLimit', res.data.overSizeLimit);

      setCoating(res.data);
    } else {
      Toast.fire({
        title: 'เกิดข้อผิดพลาด',
        text: res.message,
        icon: 'error',
      });
    }
  };

  useEffect(() => {
    form.reset();

    if (action === 'edit' && !isUndefined(id)) {
      getUnfoldedSizeById(id);
    }
  }, [action, id]);

  const onSubmit = async (values: z.infer<typeof FormSchema>) => {
    const req: UnfoldedSizeRequest = {
      name: values.name,
      width: values.width,
      height: values.height,
      overSizeLimit: values.overSizeLimit,
    };

    if (action === 'edit') {
      req.isActive = unfoldedSize.isActive;
    }

    const res: ApiResponse =
      action !== 'edit'
        ? await unfoldedSizeService.createUnfoldedSize(req)
        : await unfoldedSizeService.updateUnfoldedSize(unfoldedSize.id, req);

    if (!res.status) {
      Swal.fire({
        title: 'เกิดข้อผิดพลาด',
        text: res.message,
        icon: 'error',
        showConfirmButton: true,
        confirmButtonColor: '#000000',
        confirmButtonText: 'OK',
      }).then();
    } else {
      Swal.fire({
        title: 'สำเร็จ',
        text: res.message,
        icon: 'success',
        showCloseButton: true,
        showCancelButton: false,
        showConfirmButton: true,
        confirmButtonColor: '#000000',
        confirmButtonText: 'OK',
        reverseButtons: true,
      }).then(() => {
        push('/unfolded-size');
      });
    }
  };
  return (
    <div className="w-full h-full flex items-center justify-center">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)}>
          <div className="flex flex-col gap-[40px] w-[800px]">
            <h3 className="text-[40px] text-[#000] font-bold">
              {action !== 'edit' ? 'สร้างขนาดกางออก' : 'แก้ไขขนาดกางออก'}
            </h3>
            <div className="flex flex-col gap-[39px]">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <Label>ชื่อ</Label>
                    <FormControl>
                      <Input
                        placeholder="ชื่อขนาดกางออก"
                        className="resize-none"
                        {...field}
                        defaultValue={field.value || ''}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <div className="flex flex-row gap-[24px] w-full">
                <div className="grow">
                  <FormField
                    control={form.control}
                    name="width"
                    render={({ field }) => (
                      <FormItem>
                        <Label>กว้าง (มม.)</Label>
                        <FormControl>
                          <Input
                            type="text"
                            className="resize-none"
                            placeholder="0"
                            {...createNumberInputHandler(form, 'width', 0)}
                            onKeyDown={onKeyDown}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                <div className="grow">
                  <FormField
                    control={form.control}
                    name="height"
                    render={({ field }) => (
                      <FormItem>
                        <Label>สูง (มม.)</Label>
                        <FormControl>
                          <Input
                            type="text"
                            className="resize-none"
                            placeholder="0"
                            {...createNumberInputHandler(form, 'height', 0)}
                            onKeyDown={onKeyDown}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                <div className="grow">
                  <FormField
                    control={form.control}
                    name="overSizeLimit"
                    render={({ field }) => (
                      <FormItem>
                        <Label>ค่าความคลาดเคลื่อน (ตารางนิ้ว)</Label>
                        <FormControl>
                          <Input
                            type="text"
                            className="resize-none"
                            placeholder="0"
                            {...createNumberInputHandler(form, 'overSizeLimit', 2)}
                            onKeyDown={onKeyDown}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>
            </div>
            <div className="flex flex-row items-center justify-between gap-[16px] w-full h-full">
              <Button
                type="button"
                variant="outline"
                className="w-full"
                onClick={() => push('/unfolded-size')}
              >
                ยกเลิก
              </Button>
              <Button type="submit" className="w-full">
                บันทึก
              </Button>
            </div>
          </div>
        </form>
      </Form>
    </div>
  );
};

export default CreateUnfoldedSizeForm;
