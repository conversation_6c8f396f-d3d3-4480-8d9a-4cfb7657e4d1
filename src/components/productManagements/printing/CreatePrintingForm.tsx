'use client';

import React, { Fragment, useEffect, useState } from 'react';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';

import { Button } from '@/components/ui/button';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { ApiResponse, ValidateFileType } from '@/utils/types';
import { validateFile } from '@/utils/validateFiles';
import { isNull, isUndefined } from 'lodash';
import fileUploadService from '@/services/fileUploader';
import { Toast } from '@/utils/toast';
import ImageUploadComponent from '@/components/shared/image-upload';
import { useRouter } from 'next/navigation';
import { Label } from '@/components/ui/label';

import { CircleMinus, Plus } from 'lucide-react';
import { Separator } from '@/components/ui/separator';
import {
  inputNumberFormat,
  inputNumberFormatWithCommas,
  getFormattedDisplayValue,
  onKeyDown
} from '@/utils/input-number';
import printingService, {
  MachineItem,
  PrintingRequest,
} from '@/services/printing';
import Swal from 'sweetalert2';

type PropType = {
  action: string;
  id?: number;
};

const PrinterSchema = z.object({
  printer: z
    .string({
      required_error: 'กรุณากรอกรุ่นเครื่องพิมพ์',
    })
    .min(1, 'กรุณากรอกรุ่นเครื่องพิมพ์'),
});

const FormSchema = z
  .object({
    name: z
      .string({
        required_error: 'กรุณากรอกชื่อระบบพิมพ์',
      })
      .min(1, 'กรุณากรอกชื่อระบบพิมพ์'),
    image: z
      .string({
        required_error: 'กรุณาอัปโหลดรูปภาพ',
      })
      .min(1, 'กรุณาอัปโหลดรูปภาพ'),
    minPrint: z.coerce
      .number({
        required_error: 'กรุณากรอกจำนวนพิมพ์เริ่มต้น',
        invalid_type_error: 'กรุณากรอกจำนวนพิมพ์เริ่มต้น',
      })
      .gte(1, 'กรุณากรอกจำนวนพิมพ์เริ่มต้น 1 ใบพิมพ์'),
    maxPrint: z.coerce
      .number({
        required_error: 'กรุณากรอกจำนวนพิมพ์สูงสุด',
        invalid_type_error: 'กรุณากรอกจำนวนพิมพ์สูงสุด',
      })
      .gte(1, 'กรุณากรอกจำนวนพิมพ์สูงสุด'),
    machines: z.array(PrinterSchema),
  })
  .refine(
    (data) =>
      Number.isFinite(data.minPrint) &&
      Number.isFinite(data.maxPrint) &&
      data.maxPrint > data.minPrint,
    {
      message: 'จำนวนพิมพ์สูงสุดต้องมากกว่าจำนวนพิมพ์เริ่มต้น',
      path: ['maxPrint'],
    },
  );
const CreatePrintingForm = ({ action, id }: PropType) => {
  const { push } = useRouter();
  const [fileUpload, setFileUpload] = useState<any>(null);
  const [printing, setPrinting] = useState<any>(null);

  const form = useForm<z.infer<typeof FormSchema>>({
    resolver: zodResolver(FormSchema),
    mode: 'onChange',
    reValidateMode: 'onChange',
    defaultValues: {
      name: '',
      image: '',
      minPrint: 0,
      maxPrint: 0,
      machines: [],
    },
  });

  const getPrintingById = async (id: number) => {
    const res: ApiResponse = await printingService.getPrintingById(id);
    if (res.status) {
      form.setValue('name', res.data.name);
      form.setValue('minPrint', res.data.minPrint);
      form.setValue('maxPrint', res.data.maxPrint);
      form.setValue('image', res.data.imageUrl);
      form.setValue(
        'machines',
        res.data.machine.map((item: any) => {
          return { printer: item.name };
        }),
      );

      setFileUpload(res.data.imageUrl);
      setPrinting(res.data);
    } else {
      Toast.fire({
        title: 'เกิดข้อผิดพลาด',
        text: res.message,
        icon: 'error',
      });
    }
  };

  useEffect(() => {
    form.reset();
    form.setValue('machines', []);
    setFileUpload(null);

    if (action === 'edit' && !isUndefined(id)) {
      getPrintingById(id);
    }
  }, [action, id]);

  const onSubmit = async (values: z.infer<typeof FormSchema>) => {
    const machineConfig: MachineItem[] = [];

    values.machines.map((conf) => {
      machineConfig.push({
        name: conf.printer,
      });
    });

    const req: PrintingRequest = {
      name: values.name,
      minPrint: values.minPrint,
      maxPrint: values.maxPrint,
      imageUrl: values.image,
      machine: machineConfig,
    };

    const res: ApiResponse =
      action !== 'edit'
        ? await printingService.createPrinting(req)
        : await printingService.updatePrinting(printing.id, req);

    if (!res.status) {
      Swal.fire({
        title: 'เกิดข้อผิดพลาด',
        text: res.message,
        icon: 'error',
        showConfirmButton: true,
        confirmButtonColor: '#000000',
        confirmButtonText: 'OK',
      }).then();
    } else {
      Swal.fire({
        title: 'สำเร็จ',
        text: res.message,
        icon: 'success',
        showCloseButton: true,
        showCancelButton: false,
        showConfirmButton: true,
        confirmButtonColor: '#000000',
        confirmButtonText: 'OK',
        reverseButtons: true,
      }).then(() => {
        push('/printing');
      });
    }
  };

  const handleImageChange = async (e: any) => {
    const file = e.target.files[0];

    const validate: ValidateFileType = validateFile(e.target.files);
    if (validate.status) {
      if (!isUndefined(file) && !isNull(file)) {
        const formData: FormData = new FormData();
        formData.append('file', file);

        const res: ApiResponse = await fileUploadService.upload(formData);

        if (res.status) {
          const url = res.data;
          setFileUpload(url);
          form.setValue('image', url);
        } else {
          Toast.fire({
            title: 'เกิดข้อผิดพลาด',
            text: `ไฟล์อัปโหลดผิดพลาด :${res.message}`,
            icon: 'error',
          });
        }
      }
    } else {
      Toast.fire({
        title: 'เกิดข้อผิดพลาด',
        text: validate.message,
        icon: 'error',
      });
    }
  };

  const handleRemove = (index: number) => {
    const configs = form.watch('machines');
    configs.splice(index, 1);

    form.setValue('machines', configs);
  };

  return (
    <div className="w-full h-full flex justify-center pt-[40px] mb-[40px]">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)}>
          <div className="flex flex-col gap-[40px] w-[800px]">
            <h3 className="text-[40px] text-[#000] font-bold">
              {action !== 'edit' ? 'สร้างระบบพิมพ์' : 'แก้ไขระบบพิมพ์'}
            </h3>
            <div className="flex flex-col gap-[39px]">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <Label>ชื่อ</Label>
                    <FormControl>
                      <Input
                        placeholder="ชื่อระบบพิมพ์"
                        className="resize-none"
                        {...field}
                        defaultValue={''}
                        value={field.value || ''}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <div>
                <Label>รูปภาพ</Label>
                <ImageUploadComponent file={fileUpload}>
                  <FormField
                    control={form.control}
                    name="image"
                    render={() => (
                      <FormItem>
                        <input
                          type="file"
                          onChange={handleImageChange}
                          className="w-[140px] resize-none"
                        />
                        <FormMessage className="absolute top-[117px]" />
                      </FormItem>
                    )}
                  />
                </ImageUploadComponent>
              </div>
              <div className="flex flex-row gap-[24px] w-full">
                <div className="grow">
                  <FormField
                    control={form.control}
                    name="minPrint"
                    render={({ field }) => (
                      <FormItem>
                        <Label>จำนวนพิมพ์เริ่มต้น</Label>
                        <FormControl>
                          <Input
                            type="text"
                            className="resize-none"
                            placeholder="0"
                            {...field}
                            value={getFormattedDisplayValue(field.value, 0)}
                            onChange={(event) =>
                              inputNumberFormatWithCommas(event, form, 'minPrint', 0)
                            }
                            onKeyDown={onKeyDown}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                <div className="grow">
                  <FormField
                    control={form.control}
                    name="maxPrint"
                    render={({ field }) => (
                      <FormItem>
                        <Label>จำนวนพิมพ์สูงสุด</Label>
                        <FormControl>
                          <Input
                            type="number"
                            className="resize-none"
                            placeholder="0"
                            {...field}
                            value={field.value || ''}
                            onChange={(event) =>
                              inputNumberFormat(event, form, 'maxPrint', 0)
                            }
                            onKeyDown={onKeyDown}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>
              <div>
                <div className="flex flex-col gap-4">
                  <div className="flex flex-row items-center justify-between">
                    <Label>เครื่องพิมพ์</Label>
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() =>
                        form.setValue('machines', [
                          ...form.watch('machines'),
                          {
                            printer: '',
                          },
                        ])
                      }
                    >
                      <Plus /> เพิ่มเครื่องพิมพ์
                    </Button>
                  </div>

                  <div className="border rounded-lg overflow-hidden">
                    <div className="flex flex-row gap-[16px] g items-center flex-nowrap w-full bg-[#F5F5F5] py-[11px] text-[12px] px-[24px]">
                      <div className="flex-1/2">ชื่อรุ่น</div>
                    </div>
                    {form.watch('machines')?.map((config, index) => (
                      <Fragment key={index}>
                        <div className="flex flex-row gap-[16px] g items-start flex-nowrap w-full px-[24px] py-[16px]">
                          <div className="flex-1/2">
                            <FormField
                              control={form.control}
                              name={`machines.${index}.printer`}
                              render={({ field }) => (
                                <FormItem>
                                  <FormControl>
                                    <Input
                                      type="text"
                                      className="resize-none"
                                      placeholder="รุ่นเครื่องพิมพ์"
                                      {...field}
                                      defaultValue={''}
                                      value={field.value || ''}
                                    />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                          </div>
                          <div className="flex justify-end">
                            <Button
                              type="button"
                              variant="outline"
                              onClick={() => handleRemove(index)}
                            >
                              <CircleMinus className="text-[#d32e2e]" />
                            </Button>
                          </div>
                        </div>
                        {form.watch('machines').length !== 1 &&
                          index + 1 !== form.watch('machines').length && (
                            <div className="w-full px-[24px]">
                              <Separator />
                            </div>
                          )}
                      </Fragment>
                    ))}
                  </div>
                  <FormField
                    control={form.control}
                    name="machines"
                    render={({ field }) => (
                      <FormItem {...field}>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>
            </div>
            <div className="flex flex-row items-center justify-between gap-[16px] w-full h-full">
              <Button
                type="button"
                variant="outline"
                className="w-full"
                onClick={() => push('/printing')}
              >
                ยกเลิก
              </Button>
              <Button type="submit" className="w-full">
                บันทึก
              </Button>
            </div>
          </div>
        </form>
      </Form>
    </div>
  );
};

export default CreatePrintingForm;
