'use client';

import React, { Fragment, useEffect, useState } from 'react';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';

import { Button } from '@/components/ui/button';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { ApiResponse, ApiResponseData, ValidateFileType } from '@/utils/types';
import { validateFile } from '@/utils/validateFiles';
import { isEmpty, isNull, isUndefined } from 'lodash';
import fileUploadService from '@/services/fileUploader';
import { Toast } from '@/utils/toast';
import ImageUploadComponent from '@/components/shared/image-upload';
import { useRouter } from 'next/navigation';
import { Label } from '@/components/ui/label';
import Swal from 'sweetalert2';
import { Textarea } from '@/components/ui/textarea';
import specialTechnicService, {
  SpecialTechnicConfigRequest,
  SpecialTechnicRequest,
} from '@/services/specialTechnic';

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { CircleMinus, Plus } from 'lucide-react';
import { Separator } from '@/components/ui/separator';
import areaSizeService from '@/services/areaSize';
import {
  inputNumberFormat,
  inputNumberFormatWithCommas,
  getFormattedDisplayValue,
  createNumberInputHandler,
  onKeyDown
} from '@/utils/input-number';

type PropType = {
  action: string;
  id?: number;
};

const SpecialTechnicConfigSchema = z.object({
  price: z.coerce
    .number({
      required_error: 'กรุณากรอกราคา',
      invalid_type_error: 'กรุณากรอกราคา',
    })
    .gte(0.1, 'กรุณากรอกราคาเริ่มต้น 0.1 บาท'),
  period: z.coerce
    .number({
      required_error: 'กรุณากรอกระยะเวลา',
      invalid_type_error: 'กรุณากรอกระยะเวลา',
    })
    .gte(1, 'กรุณากรอกระยะเวลาเริ่มต้น 1 วัน'),
  areaSizePercentageId: z
    .string({
      required_error: 'กรุณาเลือกเปอร์เซ็นต์พื้นที่',
    })
    .min(1, 'กรุณาเลือกเปอร์เซ็นต์พื้นที่')
    .refine((value) => value !== 'เลือกเปอร์เซ็นต์พื้นที่', {
      message: 'กรุณาเลือกเปอร์เซ็นต์พื้นที่',
    }),
});

const FormSchema = z.object({
  name: z
    .string({
      required_error: 'กรุณากรอกชื่อเทคนิคพิเศษ',
    })
    .min(1, 'กรุณากรอกชื่อเทคนิคพิเศษ'),
  image: z
    .string({
      required_error: 'กรุณาอัปโหลดรูปภาพ',
    })
    .min(1, 'กรุณาอัปโหลดรูปภาพ'),
  description: z.string().optional().or(z.literal('')),
  specialTechnicConfig: z
    .array(SpecialTechnicConfigSchema)
    .min(1, 'ต้องมีค่าคอนฟิกเทคนิคพิเศษอย่างน้อย 1 รายการ'),
});

const CreateSpecialTechnicForm = ({ action, id }: PropType) => {
  const { push } = useRouter();
  const [fileUpload, setFileUpload] = useState<any>(null);
  const [specialTechnic, setSpecialTechnic] = useState<any>(null);
  const [areaSizeSelect, setAreaSizeSelect] = useState<any>([]);

  const form = useForm<z.infer<typeof FormSchema>>({
    resolver: zodResolver(FormSchema),
  });

  const getSpecialTechnicById = async (id: number) => {
    const res: ApiResponse =
      await specialTechnicService.getSpecialTechnicById(id);
    if (res.status) {
      setFileUpload(res.data.imageUrl);
      setSpecialTechnic(res.data);
    } else {
      Toast.fire({
        title: 'เกิดข้อผิดพลาด',
        text: res.message,
        icon: 'error',
      });
    }
  };

  const getAreaSizeSelect = async () => {
    const res: ApiResponse<ApiResponseData> =
      await areaSizeService.getAreaSizeSelect();
    if (res.status) {
      setAreaSizeSelect(res.data.content);
    } else {
      Toast.fire({
        title: 'เกิดข้อผิดพลาด',
        text: res.message,
        icon: 'error',
      });
    }
  };

  useEffect(() => {
    form.reset();
    form.setValue('specialTechnicConfig', []);
    setFileUpload(null);
    getAreaSizeSelect();

    if (action === 'edit' && !isUndefined(id)) {
      getSpecialTechnicById(id);
    }
  }, [action, id]);

  useEffect(() => {
    if (
      action === 'edit' &&
      !isNull(specialTechnic) &&
      !isEmpty(areaSizeSelect)
    ) {
      if (!isEmpty(specialTechnic.specialTechnicConfig)) {
        form.setValue('name', specialTechnic.name);
        form.setValue('image', specialTechnic.imageUrl);
        form.setValue('description', specialTechnic.description);
        const setConfigs: any[] = [];

        specialTechnic.specialTechnicConfig.forEach((config: any) => {
          const areaSize: any = areaSizeSelect.find(
            (size: any) => size.id === config.areaSizePercentageId,
          );

          if (!isUndefined(areaSize)) {
            setConfigs.push({
              price: config.price,
              period: config.period,
              areaSizePercentageId: areaSize.name,
            });
          }
        });

        if (!isEmpty(setConfigs)) {
          form.setValue('specialTechnicConfig', setConfigs);
        }
      }
    }
  }, [areaSizeSelect, specialTechnic]);

  const onSubmit = async (values: z.infer<typeof FormSchema>) => {
    const specialTechnicConfig: SpecialTechnicConfigRequest[] = [];

    values.specialTechnicConfig.map((conf) => {
      const areaSize = areaSizeSelect.find(
        (size: any) => size.name === conf.areaSizePercentageId,
      );
      specialTechnicConfig.push({
        price: conf.price,
        period: conf.period,
        areaSizePercentageId: areaSize.id,
      });
    });

    const req: SpecialTechnicRequest = {
      name: values.name,
      description: values.description || '',
      imageUrl: values.image,
      specialTechnicConfig: specialTechnicConfig,
    };

    if (action === 'edit') {
      req.isActive = specialTechnic.isActive;
    }

    const res: ApiResponse =
      action !== 'edit'
        ? await specialTechnicService.createSpecialTechnic(req)
        : await specialTechnicService.updateSpecialTechnic(
            specialTechnic.id,
            req,
          );

    if (!res.status) {
      Swal.fire({
        title: 'เกิดข้อผิดพลาด',
        text: res.message,
        icon: 'error',
        showConfirmButton: true,
        confirmButtonColor: '#000000',
        confirmButtonText: 'OK',
      }).then();
    } else {
      Swal.fire({
        title: 'สำเร็จ',
        text: res.message,
        icon: 'success',
        showCloseButton: true,
        showCancelButton: false,
        showConfirmButton: true,
        confirmButtonColor: '#000000',
        confirmButtonText: 'OK',
        reverseButtons: true,
      }).then(() => {
        push('/special-technic');
      });
    }
  };

  const handleImageChange = async (e: any) => {
    const file = e.target.files[0];

    const validate: ValidateFileType = validateFile(e.target.files);
    if (validate.status) {
      if (!isUndefined(file) && !isNull(file)) {
        const formData: FormData = new FormData();
        formData.append('file', file);

        const res: ApiResponse = await fileUploadService.upload(formData);

        if (res.status) {
          const url = res.data;
          setFileUpload(url);
          form.setValue('image', url);
        } else {
          Toast.fire({
            title: 'เกิดข้อผิดพลาด',
            text: `ไฟล์อัปโหลดผิดพลาด :${res.message}`,
            icon: 'error',
          });
        }
      }
    } else {
      Toast.fire({
        title: 'เกิดข้อผิดพลาด',
        text: validate.message,
        icon: 'error',
      });
    }
  };

  const handleRemove = (index: number) => {
    const configs = form.watch('specialTechnicConfig');
    configs.splice(index, 1);

    form.setValue('specialTechnicConfig', configs);
  };

  return (
    <div className="w-full h-full flex justify-center pt-[40px] mb-[40px]">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)}>
          <div className="flex flex-col gap-[40px] w-[800px]">
            <h3 className="text-[40px] text-[#000] font-bold">
              {action !== 'edit' ? 'สร้างเทคนิคพิเศษ' : 'แก้ไขเทคนิคพิเศษ'}
            </h3>
            <div className="flex flex-col gap-[39px]">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <Label>ชื่อ</Label>
                    <FormControl>
                      <Input
                        placeholder="ชื่อเทคนิคพิเศษ"
                        className="resize-none"
                        {...field}
                        defaultValue={field.value || ''}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <div>
                <Label>รูปภาพ</Label>
                <ImageUploadComponent file={fileUpload}>
                  <FormField
                    control={form.control}
                    name="image"
                    render={() => (
                      <FormItem>
                        <input
                          type="file"
                          onChange={handleImageChange}
                          className="w-[140px] resize-none"
                        />
                        <FormMessage className="absolute top-[117px]" />
                      </FormItem>
                    )}
                  />
                </ImageUploadComponent>
              </div>
              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <Label>รายละเอียด</Label>
                    <FormControl>
                      <Textarea
                        placeholder="อธิบายเกี่ยวกับเทคนิคพิเศษ"
                        className="resize-none"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <div>
                <div className="flex flex-col gap-4">
                  <div className="flex flex-row items-center justify-between">
                    <Label>พื้นที่</Label>
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() =>
                        form.setValue('specialTechnicConfig', [
                          ...form.watch('specialTechnicConfig'),
                          {
                            price: 0,
                            period: 0,
                            areaSizePercentageId: 'เลือกเปอร์เซ็นต์พื้นที่',
                          },
                        ])
                      }
                    >
                      <Plus /> เพิ่มพื้นที่
                    </Button>
                  </div>

                  <div className="border rounded-lg overflow-hidden">
                    <div className="flex flex-row gap-[16px] g items-center flex-nowrap w-full bg-[#F5F5F5] py-[11px] text-[12px] px-[24px]">
                      <div className="flex-1/2">ขนาดพื้นที่ (%)</div>
                      <div className="flex-1/2">การผลิต (วัน)</div>
                      <div className="flex-1/2">ราคา (บาท)</div>
                      <div className="flex justify-end">ลบ</div>
                    </div>
                    {form
                      .watch('specialTechnicConfig')
                      ?.map((config, index) => (
                        <Fragment
                          key={
                            config.price * index ||
                            `area-config-${config.price}-${(index + 1) * 2}`
                          }
                        >
                          <div className="flex flex-row gap-[16px] g items-start flex-nowrap w-full px-[24px] py-[16px]">
                            <div className="flex-1/2">
                              <FormField
                                control={form.control}
                                name={`specialTechnicConfig.${index}.areaSizePercentageId`}
                                render={({ field }) => (
                                  <FormItem>
                                    <Select
                                      onValueChange={field.onChange}
                                      defaultValue={field.value}
                                      value={field.value}
                                    >
                                      <FormControl>
                                        <SelectTrigger className="mb-0">
                                          <SelectValue placeholder="เลือกเปอร์เซ็นต์พื้นที่" />
                                        </SelectTrigger>
                                      </FormControl>
                                      <SelectContent>
                                        <SelectItem value="เลือกเปอร์เซ็นต์พื้นที่">
                                          เลือกเปอร์เซ็นต์พื้นที่
                                        </SelectItem>
                                        {!isEmpty(areaSizeSelect) &&
                                          areaSizeSelect.map(
                                            (item: any, index: number) => (
                                              <SelectItem
                                                key={index}
                                                value={item.name}
                                              >
                                                {item.name}
                                              </SelectItem>
                                            ),
                                          )}
                                      </SelectContent>
                                    </Select>
                                    <FormMessage className="pt-2" />
                                  </FormItem>
                                )}
                              />
                            </div>
                            <div className="flex-1/2">
                              <FormField
                                control={form.control}
                                name={`specialTechnicConfig.${index}.period`}
                                render={({ field }) => (
                                  <FormItem>
                                    <FormControl>
                                      <Input
                                        type="text"
                                        className="resize-none"
                                        placeholder="ระยะเวลา"
                                        {...createNumberInputHandler(
                                          form,
                                          `specialTechnicConfig.${index}.period`,
                                          0
                                        )}
                                        onKeyDown={onKeyDown}
                                      />
                                    </FormControl>
                                    <FormMessage />
                                  </FormItem>
                                )}
                              />
                            </div>
                            <div className="flex-1/2">
                              <FormField
                                control={form.control}
                                name={`specialTechnicConfig.${index}.price`}
                                render={({ field }) => (
                                  <FormItem>
                                    <FormControl>
                                      <Input
                                        type="text"
                                        className="resize-none"
                                        placeholder="ราคา"
                                        {...createNumberInputHandler(
                                          form,
                                          `specialTechnicConfig.${index}.price`,
                                          0
                                        )}
                                        onKeyDown={onKeyDown}
                                      />
                                    </FormControl>
                                    <FormMessage />
                                  </FormItem>
                                )}
                              />
                            </div>
                            <div className="flex justify-end">
                              <Button
                                type="button"
                                variant="outline"
                                onClick={() => handleRemove(index)}
                              >
                                <CircleMinus className="text-[#d32e2e]" />
                              </Button>
                            </div>
                          </div>
                          {form.watch('specialTechnicConfig').length !== 1 &&
                            index + 1 !==
                              form.watch('specialTechnicConfig').length && (
                              <div className="w-full px-[24px]">
                                <Separator />
                              </div>
                            )}
                        </Fragment>
                      ))}
                  </div>
                  <FormField
                    control={form.control}
                    name="specialTechnicConfig"
                    render={({ field }) => (
                      <FormItem {...field}>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>
            </div>
            <div className="flex flex-row items-center justify-between gap-[16px] w-full h-full">
              <Button
                type="button"
                variant="outline"
                className="w-full"
                onClick={() => push('/special-technic')}
              >
                ยกเลิก
              </Button>
              <Button type="submit" className="w-full">
                บันทึก
              </Button>
            </div>
          </div>
        </form>
      </Form>
    </div>
  );
};

export default CreateSpecialTechnicForm;
