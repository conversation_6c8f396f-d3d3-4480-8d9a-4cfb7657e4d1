export const validateNumber = (value: any, decimalPlaces = 0) => {
  // ถ้าค่าว่าง ให้ set เป็น empty string
  if (value === '') {
    return '';
  }

  // Remove commas for validation
  const cleanValue = String(value).replace(/,/g, '');

  // Regex patterns
  const negativeRegex = /^-/; // ตรวจสอบเครื่องหมายลบ
  const validNumberRegex =
    decimalPlaces > 0
      ? new RegExp(`^\\d*\\.?\\d{0,${decimalPlaces}}$`) // อนุญาตทศนิยมตามที่กำหนด
      : /^\d*\.?\d*$/; // อนุญาตทศนิยมเพื่อให้สามารถตัดออกได้

  // ตรวจสอบว่ามีเครื่องหมายลบหรือไม่
  if (negativeRegex.test(cleanValue)) {
    // ถ้ามีเครื่องหมายลบ ไม่อนุญาต
    return '';
  }

  // ตรวจสอบรูปแบบตัวเลข
  if (validNumberRegex.test(cleanValue)) {
    // ถ้า decimalPlaces = 0 และมีจุดทศนิยม ให้ตัดจุดทศนิยมและตัวเลขหลังจุดออก
    if (decimalPlaces === 0 && cleanValue.includes('.')) {
      return cleanValue.split('.')[0];
    }
    return cleanValue;
  }

  // ถ้าไม่ผ่านการตรวจสอบ ให้ return empty string
  return '';
};

export const onKeyDown = (event: any) => {
  // บล็อคคีย์ "-" (minus sign) ทั้งใน main keyboard และ numpad
  if (event.key === '-' || event.key === 'Minus') {
    event.preventDefault();
  }

  // บล็อคคีย์ "e" และ "E" (scientific notation)
  if (event.key === 'e' || event.key === 'E') {
    event.preventDefault();
  }

  // บล็อคคีย์ "+" (plus sign)
  if (event.key === '+' || event.key === 'Plus') {
    event.preventDefault();
  }
};

// Format number with commas for display
export const formatNumberWithCommas = (value: any, decimalPlaces = 0) => {
  if (value === '' || value === null || value === undefined) {
    return '';
  }

  const numValue = Number(value);
  if (isNaN(numValue)) {
    return '';
  }

  // Format with commas using Intl.NumberFormat
  return new Intl.NumberFormat('en-US', {
    maximumFractionDigits: decimalPlaces,
    minimumFractionDigits: 0,
  }).format(numValue);
};

// Remove commas from formatted number to get clean numeric value
export const removeCommas = (value: string) => {
  return value.replace(/,/g, '');
};

export const inputNumberFormat = (
  event: any,
  form: any,
  name: any,
  digit: number,
) => {
  const value: number | any = validateNumber(event.target.value, digit);

  if (value !== null && value >= 0) {
    form.setValue(name, value);
  } else {
    form.setValue(name, 0.0);
  }
};

// Enhanced input number format with comma display
export const inputNumberFormatWithCommas = (
  event: any,
  form: any,
  name: any,
  digit: number,
) => {
  const inputValue = event.target.value;
  const cleanValue = validateNumber(inputValue, digit);

  if (cleanValue !== null && cleanValue !== '' && Number(cleanValue) >= 0) {
    // Set the actual numeric value for form submission
    form.setValue(name, cleanValue);
  } else if (cleanValue === '') {
    // Handle empty input
    form.setValue(name, '');
  } else {
    // Invalid input, prevent the change by setting to previous valid value
    const currentValue = form.getValues(name);
    if (currentValue !== undefined && currentValue !== '') {
      form.setValue(name, currentValue);
    } else {
      form.setValue(name, '');
    }
  }
};

// Get formatted display value for controlled inputs
export const getFormattedDisplayValue = (value: any, digit: number = 0) => {
  if (value === '' || value === null || value === undefined) {
    return '';
  }
  return formatNumberWithCommas(value, digit);
};

// Create a controlled number input handler that maintains formatting
export const createNumberInputHandler = (
  form: any,
  name: any,
  digit: number,
) => {
  return {
    value: getFormattedDisplayValue(form.watch(name), digit),
    onChange: (event: any) => {
      const inputValue = event.target.value;
      const cleanValue = validateNumber(inputValue, digit);

      if (cleanValue !== null && cleanValue !== '' && Number(cleanValue) >= 0) {
        form.setValue(name, cleanValue);
      } else if (cleanValue === '') {
        form.setValue(name, '');
      }
      // For invalid input, don't update the form value
    },
    onPaste: (event: any) => {
      event.preventDefault();
      const pastedText = (event.clipboardData || window.clipboardData).getData('text');
      const cleanValue = validateNumber(pastedText, digit);

      if (cleanValue !== null && cleanValue !== '' && Number(cleanValue) >= 0) {
        form.setValue(name, cleanValue);
      } else if (cleanValue === '') {
        form.setValue(name, '');
      }
    }
  };
};

// Handle paste events for number inputs with comma formatting
export const handleNumberPaste = (
  event: any,
  form: any,
  name: any,
  digit: number,
) => {
  event.preventDefault();

  // Get pasted text
  const pastedText = (event.clipboardData || window.clipboardData).getData('text');
  const cleanValue = validateNumber(pastedText, digit);

  if (cleanValue !== null && cleanValue !== '' && Number(cleanValue) >= 0) {
    // Set the actual numeric value for form submission
    form.setValue(name, cleanValue);
  } else if (cleanValue === '') {
    // Handle empty paste
    form.setValue(name, '');
  }
  // If invalid, do nothing (paste is prevented)
};
